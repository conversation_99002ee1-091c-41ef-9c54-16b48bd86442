#!/usr/bin/env python3
"""Debug script to check configuration loading."""

import os
from dotenv import load_dotenv
from ai_cli.config.manager import ConfigManager

# Load environment variables
load_dotenv()

def debug_config():
    """Debug configuration loading."""
    print("🔍 Configuration Debug")
    print("=" * 40)

    # Check environment variables
    print("Environment Variables:")
    deepseek_key = os.getenv("DEEPSEEK_API_KEY")
    print(f"  DEEPSEEK_API_KEY: {deepseek_key[:10] + '...' if deepseek_key else 'NOT SET'}")

    ollama_url = os.getenv("OLLAMA_BASE_URL")
    print(f"  OLLAMA_BASE_URL: {ollama_url if ollama_url else 'NOT SET'}")

    # Load configuration
    print("\nLoading Configuration...")
    config_manager = ConfigManager()

    # Check which config file is being used
    config_path = config_manager._find_config_path()
    print(f"Config file path: {config_path}")

    config = config_manager.load_config()

    print(f"\nConfiguration Loaded:")
    print(f"  Default Provider: {config.llm.default_provider}")
    print(f"  Deepseek Config: {config.llm.deepseek}")
    print(f"  Ollama Config: {config.llm.ollama}")

    # Check specific API key
    deepseek_api_key = config.llm.deepseek.get("api_key")
    print(f"\nDeepseek API Key from config: {deepseek_api_key[:10] + '...' if deepseek_api_key else 'NOT SET'}")

    # Test the condition that's failing
    has_api_key = bool(config.llm.deepseek.get("api_key"))
    print(f"Has API Key (condition check): {has_api_key}")

    if has_api_key:
        print("✅ Deepseek provider should be initialized")
    else:
        print("❌ Deepseek provider will NOT be initialized")
        print("   This is why you're getting the error!")

if __name__ == "__main__":
    debug_config()
