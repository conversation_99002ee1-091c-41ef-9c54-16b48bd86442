#!/usr/bin/env python3
"""Test script to diagnose Deepseek API issues."""

import asyncio
import os
import httpx
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_deepseek_api():
    """Test Deepseek API connectivity and authentication."""
    
    # Get API key from environment
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not found in environment variables")
        return False
    
    print(f"✓ API Key found: {api_key[:10]}...")
    
    # Test different base URLs and models
    base_urls = [
        "https://api.deepseek.com/v1",
        "https://api.deepseek.com"
    ]
    
    models = [
        "deepseek-chat",
        "deepseek-reasoner", 
        "deepseek-coder"
    ]
    
    for base_url in base_urls:
        print(f"\n🔍 Testing base URL: {base_url}")
        
        client = httpx.AsyncClient(
            base_url=base_url,
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
            },
            timeout=30.0,
        )
        
        try:
            # Test 1: Check models endpoint
            print("  📋 Testing /models endpoint...")
            response = await client.get("/models")
            print(f"     Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"     Available models: {len(data.get('data', []))}")
                for model in data.get('data', [])[:3]:  # Show first 3 models
                    print(f"       - {model.get('id', 'unknown')}")
            else:
                print(f"     Error: {response.text}")
                
        except Exception as e:
            print(f"     ❌ Models endpoint failed: {e}")
        
        # Test 2: Try chat completions with different models
        for model in models:
            print(f"  💬 Testing chat completions with model: {model}")
            
            try:
                payload = {
                    "model": model,
                    "messages": [
                        {"role": "user", "content": "Hello, can you respond with just 'OK'?"}
                    ],
                    "max_tokens": 10,
                    "temperature": 0.1
                }
                
                response = await client.post("/chat/completions", json=payload)
                print(f"     Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                    print(f"     ✓ Response: {content.strip()}")
                    print(f"     ✓ Model {model} works with {base_url}")
                    return True
                else:
                    print(f"     ❌ Error: {response.text}")
                    
            except Exception as e:
                print(f"     ❌ Chat completion failed: {e}")
        
        await client.aclose()
    
    return False

async def main():
    """Main test function."""
    print("🧪 Deepseek API Diagnostic Test")
    print("=" * 40)
    
    success = await test_deepseek_api()
    
    if success:
        print("\n✅ Deepseek API is working correctly!")
    else:
        print("\n❌ Deepseek API test failed. Check:")
        print("   1. API key validity")
        print("   2. Network connectivity") 
        print("   3. Model names")
        print("   4. Base URL")

if __name__ == "__main__":
    asyncio.run(main())
