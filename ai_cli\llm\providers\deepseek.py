"""Deepseek LLM provider implementation."""

import json
import logging
from typing import Any, Async<PERSON>enerator, Dict, List, Optional

import httpx

from ai_cli.llm.providers.base import BaseLL<PERSON>rovider, LLMMessage, LLMResponse
from ai_cli.utils.exceptions import LLMError

logger = logging.getLogger(__name__)


class DeepseekProvider(BaseLLMProvider):
    """Deepseek API provider implementation."""

    def __init__(self, config: Dict[str, Any]) -> None:
        """Initialize Deepseek provider.

        Args:
            config: Deepseek configuration containing api_key, model, base_url
        """
        super().__init__(config)
        self.api_key = config.get("api_key", "")
        self.model = config.get("model", "deepseek-chat")
        self.base_url = config.get("base_url", "https://api.deepseek.com/v1")

        if not self.api_key:
            logger.warning("Deepseek API key not provided")

        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            },
            timeout=30.0,
        )

    async def generate_response(
        self,
        messages: List[LLMMessage],
        tools: Optional[List[Dict[str, Any]]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
    ) -> LLMResponse:
        """Generate response from Deepseek API.

        Args:
            messages: Conversation messages
            tools: Available tools/functions
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            stream: Whether to stream response

        Returns:
            LLM response object
        """
        try:
            payload = {
                "model": self.model,
                "messages": [self._convert_message(msg) for msg in messages],
                "temperature": temperature,
                "stream": stream,
            }

            if max_tokens:
                payload["max_tokens"] = max_tokens

            if tools:
                payload["tools"] = tools
                payload["tool_choice"] = "auto"

            response = await self.client.post("/chat/completions", json=payload)
            response.raise_for_status()

            data = response.json()
            choice = data["choices"][0]
            message = choice["message"]

            return LLMResponse(
                content=message.get("content", ""),
                tool_calls=message.get("tool_calls"),
                finish_reason=choice.get("finish_reason", "stop"),
                usage=data.get("usage"),
            )

        except httpx.HTTPStatusError as e:
            error_text = e.response.text if hasattr(e.response, 'text') else str(e.response.content)
            logger.error(f"Deepseek API HTTP error: {e.response.status_code} - {error_text}")
            raise LLMError(f"Deepseek API HTTP error {e.response.status_code}: {error_text}")
        except httpx.TimeoutException as e:
            logger.error(f"Deepseek API timeout: {e}")
            raise LLMError(f"Deepseek API timeout: {str(e)}")
        except httpx.ConnectError as e:
            logger.error(f"Deepseek API connection error: {e}")
            raise LLMError(f"Deepseek API connection error: {str(e)}")
        except Exception as e:
            logger.error(f"Error calling Deepseek API: {type(e).__name__}: {e}")
            raise LLMError(f"Failed to call Deepseek API: {type(e).__name__}: {str(e)}")

    async def stream_response(
        self,
        messages: List[LLMMessage],
        tools: Optional[List[Dict[str, Any]]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
    ) -> AsyncGenerator[str, None]:
        """Stream response from Deepseek API.

        Args:
            messages: Conversation messages
            tools: Available tools/functions
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate

        Yields:
            Response chunks as they become available
        """
        try:
            payload = {
                "model": self.model,
                "messages": [self._convert_message(msg) for msg in messages],
                "temperature": temperature,
                "stream": True,
            }

            if max_tokens:
                payload["max_tokens"] = max_tokens

            if tools:
                payload["tools"] = tools
                payload["tool_choice"] = "auto"

            async with self.client.stream("POST", "/chat/completions", json=payload) as response:
                response.raise_for_status()

                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix

                        if data_str.strip() == "[DONE]":
                            break

                        try:
                            data = json.loads(data_str)
                            choice = data["choices"][0]
                            delta = choice.get("delta", {})

                            if "content" in delta and delta["content"]:
                                yield delta["content"]

                        except json.JSONDecodeError:
                            continue

        except httpx.HTTPStatusError as e:
            error_text = e.response.text if hasattr(e.response, 'text') else str(e.response.content)
            logger.error(f"Deepseek streaming HTTP error: {e.response.status_code} - {error_text}")
            raise LLMError(f"Deepseek streaming HTTP error {e.response.status_code}: {error_text}")
        except httpx.TimeoutException as e:
            logger.error(f"Deepseek streaming timeout: {e}")
            raise LLMError(f"Deepseek streaming timeout: {str(e)}")
        except httpx.ConnectError as e:
            logger.error(f"Deepseek streaming connection error: {e}")
            raise LLMError(f"Deepseek streaming connection error: {str(e)}")
        except Exception as e:
            logger.error(f"Error streaming from Deepseek: {type(e).__name__}: {e}")
            raise LLMError(f"Failed to stream from Deepseek: {type(e).__name__}: {str(e)}")

    async def is_available(self) -> bool:
        """Check if Deepseek API is available.

        Returns:
            True if API is available and accessible
        """
        if not self.api_key:
            logger.debug("Deepseek is_available: No API key")
            return False

        try:
            logger.debug("Deepseek is_available: Checking /models endpoint")
            response = await self.client.get("/models")
            logger.debug(f"Deepseek is_available: Response status {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"Deepseek is_available: Exception {e}")
            return False

    def get_model_info(self) -> Dict[str, Any]:
        """Get Deepseek model information.

        Returns:
            Model information dictionary
        """
        return {
            "provider": "deepseek",
            "model": self.model,
            "base_url": self.base_url,
            "supports_tools": True,
            "supports_streaming": True,
        }

    def _convert_message(self, message: LLMMessage) -> Dict[str, Any]:
        """Convert internal message format to Deepseek API format.

        Args:
            message: Internal message object

        Returns:
            Deepseek API message format
        """
        result = {
            "role": message.role,
            "content": message.content,
        }

        if message.tool_calls:
            result["tool_calls"] = message.tool_calls

        if message.tool_call_id:
            result["tool_call_id"] = message.tool_call_id

        return result

    async def cleanup(self) -> None:
        """Clean up HTTP client."""
        await self.client.aclose()
